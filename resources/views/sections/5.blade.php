<div class="section blog-details-section section-padding">
    <div class="container">
        <div class="blog-details-wrap">
            <div class="row">
                <div class="col-lg-8">
                    <div class="blog-details-post">
                        <div class="single-blog-post single-blog">
                            <h3 class="title-blog">{{data_get($data,"title")}}</h3>
                            <span class="mb-5 float-left">{{data_get($data,"created_at")->locale(LaravelLocalization::getCurrentLocale())->isoFormat("DD MMMM YYYY")}}</span>
                            <div class="blog-image">
                                <a href="blog-details.html">
                                    <img src="{{Storage::url(data_get($data,"image"))}}" alt="{{data_get($data,"title")}}">
                                </a>
                            </div>
                            <div class="blog-content">
                                <div id="context">{!! cleanHtml(data_get($data,"text")) !!}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="blog-sidebar">
                        <div class="sidebar-widget sidebar-widget-1">
                            <form class="search-form" id="blog-search-form">
                                <input type="text" name="q" id="blog-search-input" placeholder="@lang('common.search_word')" class="highlight" required>
                                <button type="submit"><i class="icofont-search"></i></button>
                            </form>
                        </div>
                        <div class="sidebar-widget">
                            <div class="widget-title">
                                <h3 class="title" id="sidebar-title">@lang("common.related")</h3>
                            </div>
                            <div class="recent-posts">
                                <ul id="blog-results">
                                    @foreach(\App\Models\Blog::where('id', '<>', $data->id)->limit(3)->get() as $r)
                                    <li>
                                        <a class="post-link" href="{{ pageUrl(3) }}/{{$r->id}}">
                                            <div class="post-thumb">
                                                <img src="{{Storage::url($r->image)}}" alt="{{$r->title}}">
                                            </div>
                                            <div class="post-text">
                                                <h4 class="title">{{$r->title}}</h4>
                                                <span class="post-meta">
                                                    <i class="icofont-calendar"></i>
                                                    {{$r->created_at->locale(LaravelLocalization::getCurrentLocale())->isoFormat("D MMM YYYY")}}
                                                </span>
                                            </div>
                                        </a>
                                    </li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<section class="section partners">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="section-title">
                    <span>Embark on a journey of growth and learning: explore our array of upcoming programs</span>
                    <h1>Upcoming Training Courses</h1>
                    <ul>
                        <li class="s-p"><a href="#"><i class="icofont-rounded-left"></i></a></li>
                        <li class="s-n"><a href="#"><i class="icofont-rounded-right"></i></a></li>
                    </ul>
                </div>
            </div>
            <div class="col-md-12">
                <div class="partners-slider">
                    <div class="courses-slider swiper-container">
                        <div class="swiper-wrapper">
                            @foreach(\App\Models\CourseLocation::oldest('start_at')->hasCourse()->limit(10)->get() as $item)
                            <div class="swiper-slide">
                                @include('includes.course-item')
                            </div>
                            @endforeach
                        </div>
                        <div class="swiper-pag swiper-pagination1"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchForm = document.getElementById('blog-search-form');
    const searchInput = document.getElementById('blog-search-input');
    const blogResults = document.getElementById('blog-results');
    const sidebarTitle = document.getElementById('sidebar-title');

    // Store original content
    const originalResults = blogResults.innerHTML;
    const originalTitle = sidebarTitle.textContent;

    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const query = searchInput.value.trim();
        if (!query) return;

        // Show loading
        blogResults.innerHTML = '<li style="text-align: center; padding: 20px;"><i class="icofont-spinner-alt-3" style="animation: spin 1s linear infinite;"></i> Searching...</li>';
        sidebarTitle.textContent = 'Search Results';

        // Make AJAX request
        fetch(`{{ route('blog.search') }}?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                if (data.blogs && data.blogs.length > 0) {
                    let html = '';
                    data.blogs.forEach(blog => {
                        html += `
                            <li>
                                <a class="post-link" href="${blog.url}">
                                    <div class="post-thumb">
                                        <img src="${blog.image || '/default-blog-image.jpg'}" alt="${blog.title}">
                                    </div>
                                    <div class="post-text">
                                        <h4 class="title">${blog.title}</h4>
                                        <span class="post-meta">
                                            <i class="icofont-calendar"></i> ${blog.created_at}
                                        </span>
                                    </div>
                                </a>
                            </li>
                        `;
                    });
                    blogResults.innerHTML = html;
                    sidebarTitle.textContent = `Search Results (${data.blogs.length})`;
                } else {
                    blogResults.innerHTML = '<li style="text-align: center; padding: 20px; color: #666;">No blogs found</li>';
                    sidebarTitle.textContent = 'Search Results (0)';
                }
            })
            .catch(error => {
                console.error('Search error:', error);
                blogResults.innerHTML = '<li style="text-align: center; padding: 20px; color: #ff0000;">Search failed. Please try again.</li>';
            });
    });

    // Clear search functionality
    searchInput.addEventListener('input', function() {
        if (this.value === '') {
            blogResults.innerHTML = originalResults;
            sidebarTitle.textContent = originalTitle;
        }
    });
});
</script>

<style>
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.search-form {
    position: relative;
}

.search-form input:focus {
    outline: none;
    border-color: #007cba;
}

.post-link:hover .title {
    color: #007cba;
}
</style>