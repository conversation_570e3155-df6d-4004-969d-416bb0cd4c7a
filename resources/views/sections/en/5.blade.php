<section class="section section-xxl">
    <div class="container">
        <!-- Search Section -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="search-container" style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
                    <form action="{{ route('search') }}" method="GET" class="search-form">
                        <div class="input-group">
                            <input name="q" type="text" class="form-control" placeholder="Enter keyword..." style="height: 50px; font-size: 16px;" required>
                            <div class="input-group-append">
                                <button type="submit" class="btn btn-primary" style="height: 50px; padding: 0 30px;">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Related Blogs Section -->
        <div class="row">
            <div class="col-md-12">
                <h2 style="margin-bottom: 30px;">Related</h2>
            </div>
            {{-- $data here will likely be $relatedBlogs from show method --}}
            @foreach($data as $item_of_model)
                <div class="col-md-4">
                    <article class="pbmit-service-style-3">
                        <div class="pbminfotech-post-item">
                            <div class="pbmit-featured-wrapper">
                                <img src="{{ Storage::url(data_get($item_of_model,"image")) }}" class="img-fluid" alt="{{ data_get($item_of_model,"title") }}">
                            </div>
                            <div class="pbminfotech-box-content">
                                <div class="pbminfotech-box-content-inner">
                                    <div class="pbmit-service-cat">
                                        <a href="{{ pageUrl(3) }}/{{ data_get($item_of_model, 'id') }}" rel="tag">{{ data_get($item_of_model,"title") }}</a>
                                    </div>
                                    <h3 class="pbmit-service-title">
                                        <a href="{{ pageUrl(3) }}/{{ data_get($item_of_model, 'id') }}">{{ data_get($item_of_model,"text_small") ?? data_get($item_of_model, 'description') }}</a> {{-- Added fallback for text_small --}}
                                    </h3>
                                </div>
                            </div>
                        </div>
                    </article>
                </div>
            @endforeach
        </div>
    </div>
</section>