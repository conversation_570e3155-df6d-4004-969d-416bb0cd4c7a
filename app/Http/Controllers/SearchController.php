<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use App\Models\Course;
use App\Models\Conference;
use App\Models\Blog;
use App\Models\Festival;
use App\Models\Awward;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;

class SearchController extends Controller
{
    private function createSlug($title)
    {
        return Str::slug($title, '-');
    }

    public function index(Request $request)
    {
        $query = $request->get('q');
        
        if (empty($query)) {
            return redirect()->back();
        }

        $results = [];

        // Search in Courses (only future courses)
        $courses = Course::where(function($q) use ($query) {
                $q->where('title', 'LIKE', "%{$query}%")
                  ->orWhere('overview', 'LIKE', "%{$query}%")
                  ->orWhere('outline', 'LIKE', "%{$query}%");
            })
            ->whereHas('courseLocations')
            ->limit(10)
            ->get();

        foreach ($courses as $course) {
            $results[] = [
                'type' => 'Course',
                'title' => $course->title,
                'description' => strip_tags($course->overview ?: ''),
                'url' => route('course.show', $this->createSlug($course->title)),
                'image' => $course->image ? \Storage::url($course->image) : null,
            ];
        }

        // Search in Conferences (only future conferences)
        $conferences = Conference::where(function($q) use ($query) {
                $q->where('title', 'LIKE', "%{$query}%")
                  ->orWhere('overview', 'LIKE', "%{$query}%")
                  ->orWhere('outline', 'LIKE', "%{$query}%");
            })
            ->whereHas('upComing', function($q) {
                $q->where('start_at', '>', now());
            })
            ->limit(10)
            ->get();

        foreach ($conferences as $conference) {
            $results[] = [
                'type' => 'Conference',
                'title' => $conference->title,
                'description' => strip_tags($conference->overview ?: ''),
                'url' => route('conference.show', $this->createSlug($conference->title)),
                'image' => $conference->cover ? \Storage::url($conference->cover) : null,
            ];
        }

        // Search in Blogs
        $blogs = Blog::where('title', 'LIKE', "%{$query}%")
            ->orWhere('text', 'LIKE', "%{$query}%")
            ->orWhere('description', 'LIKE', "%{$query}%")
            ->limit(10)
            ->get();

        foreach ($blogs as $blog) {
            $results[] = [
                'type' => 'Blog',
                'title' => $blog->title,
                'description' => strip_tags($blog->text ?: $blog->description ?: ''),
                'url' => pageUrl(3) . '/' . $blog->id,
                'image' => $blog->image ? \Storage::url($blog->image) : null,
            ];
        }

        // Search in Festivals (only future festivals)
        $festivals = Festival::where(function($q) use ($query) {
                $q->where('title', 'LIKE', "%{$query}%")
                  ->orWhere('overview', 'LIKE', "%{$query}%")
                  ->orWhere('outline', 'LIKE', "%{$query}%");
            })
            ->whereHas('upComing', function($q) {
                $q->where('start_at', '>', now());
            })
            ->limit(10)
            ->get();

        foreach ($festivals as $festival) {
            $results[] = [
                'type' => 'Festival',
                'title' => $festival->title,
                'description' => strip_tags($festival->overview ?: ''),
                'url' => route('festival.show', $this->createSlug($festival->title)),
                'image' => $festival->cover ? \Storage::url($festival->cover) : null,
            ];
        }

        // Search in Awards (only future awards)
        $awards = Awward::where(function($q) use ($query) {
                $q->where('title', 'LIKE', "%{$query}%")
                  ->orWhere('overview', 'LIKE', "%{$query}%")
                  ->orWhere('outline', 'LIKE', "%{$query}%");
            })
            ->whereHas('upComing', function($q) {
                $q->where('start_at', '>', now());
            })
            ->limit(10)
            ->get();

        foreach ($awards as $award) {
            $results[] = [
                'type' => 'Award',
                'title' => $award->title,
                'description' => strip_tags($award->overview ?: ''),
                'url' => route('awward.show', $this->createSlug($award->title)),
                'image' => $award->cover ? \Storage::url($award->cover) : null,
            ];
        }

        return view('search.results', compact('results', 'query'));
    }

    public function blogSearch(Request $request)
    {
        $query = $request->get('q');

        if (empty($query)) {
            return redirect()->back();
        }

        // Search only in Blogs
        $blogs = Blog::where('title', 'LIKE', "%{$query}%")
            ->orWhere('text', 'LIKE', "%{$query}%")
            ->orWhere('description', 'LIKE', "%{$query}%")
            ->latest()
            ->get();

        return response()->json([
            'blogs' => $blogs->map(function($blog) {
                return [
                    'id' => $blog->id,
                    'title' => $blog->title,
                    'description' => strip_tags($blog->text ?: $blog->description ?: ''),
                    'url' => pageUrl(3) . '/' . $blog->id,
                    'image' => $blog->image ? \Storage::url($blog->image) : null,
                    'created_at' => $blog->created_at->locale(LaravelLocalization::getCurrentLocale())->isoFormat("D MMM YYYY")
                ];
            }),
            'query' => $query
        ]);
    }
}
